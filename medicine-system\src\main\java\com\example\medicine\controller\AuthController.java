package com.example.medicine.controller;

import com.example.medicine.common.Result;
import com.example.medicine.dto.LoginRequest;
import com.example.medicine.dto.LoginResponse;
import com.example.medicine.dto.UserProfileDto;
import com.example.medicine.service.AuthService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/auth")
@CrossOrigin(origins = "http://localhost:3000")
public class AuthController {
    
    @Autowired
    private AuthService authService;
    
    @PostMapping("/login")
    public Result<LoginResponse> login(@RequestBody LoginRequest loginRequest) {
        try {
            LoginResponse response = authService.login(loginRequest);
            return Result.success(response);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }
    
    @PostMapping("/logout")
    public Result<Void> logout(@RequestHeader(value = "Authorization", required = false) String token) {
        try {
            if (token != null && token.startsWith("Bearer ")) {
                token = token.substring(7);
                authService.logout(token);
            }
            return Result.success(null);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }
    
    @GetMapping("/user/info")
    public Result<LoginResponse.UserInfo> getUserInfo(@RequestHeader(value = "Authorization", required = false) String token) {
        try {
            // 这里可以根据token获取用户信息
            // 暂时返回一个示例响应
            return Result.success(new LoginResponse.UserInfo(1L, "hhh", "超级管理员", "active"));
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @GetMapping("/profile")
    public Result<UserProfileDto> getUserProfile(@RequestHeader(value = "Authorization", required = false) String token) {
        try {
            UserProfileDto profile = authService.getUserProfile(token);
            return Result.success(profile);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @PutMapping("/profile")
    public Result<UserProfileDto> updateUserProfile(
            @RequestHeader(value = "Authorization", required = false) String token,
            @RequestBody UserProfileDto profileDto) {
        try {
            UserProfileDto updatedProfile = authService.updateUserProfile(token, profileDto);
            return Result.success(updatedProfile);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }
}
