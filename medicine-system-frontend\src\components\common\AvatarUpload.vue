<template>
  <div class="avatar-upload">
    <el-upload
      class="avatar-uploader"
      :action="uploadUrl"
      :headers="uploadHeaders"
      :show-file-list="false"
      :on-success="handleAvatarSuccess"
      :on-error="handleAvatarError"
      :before-upload="beforeAvatarUpload"
      :disabled="uploading"
    >
      <div class="avatar-container">
        <el-avatar
          v-if="avatarUrl"
          :size="size"
          :src="avatarUrl"
          class="avatar"
        />
        <el-avatar
          v-else
          :size="size"
          class="avatar avatar-placeholder"
        >
          <el-icon><User /></el-icon>
        </el-avatar>
        
        <div class="avatar-overlay">
          <el-icon v-if="!uploading"><Camera /></el-icon>
          <el-icon v-else class="is-loading"><Loading /></el-icon>
        </div>
      </div>
    </el-upload>
    
    <div class="upload-tips" v-if="showTips">
      <p>支持 JPG、PNG 格式，文件大小不超过 2MB</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { User, Camera, Loading } from '@element-plus/icons-vue';
import { uploadAvatar } from '@/api/modules/auth';
import type { AvatarUploadResult } from '@/types';

interface Props {
  modelValue?: string;
  size?: number;
  showTips?: boolean;
}

interface Emits {
  (e: 'update:modelValue', value: string): void;
  (e: 'success', result: AvatarUploadResult): void;
  (e: 'error', error: any): void;
}

const props = withDefaults(defineProps<Props>(), {
  size: 100,
  showTips: true
});

const emit = defineEmits<Emits>();

const uploading = ref(false);

// 计算属性
const avatarUrl = computed({
  get: () => props.modelValue,
  set: (value: string) => emit('update:modelValue', value)
});

const uploadUrl = computed(() => {
  // 这里使用一个占位URL，实际上传会通过beforeUpload拦截
  return '/api/auth/upload-avatar';
});

const uploadHeaders = computed(() => {
  const token = localStorage.getItem('token');
  return {
    'Authorization': `Bearer ${token}`
  };
});

// 上传前验证
const beforeAvatarUpload = (file: File) => {
  const isJPG = file.type === 'image/jpeg';
  const isPNG = file.type === 'image/png';
  const isLt2M = file.size / 1024 / 1024 < 2;

  if (!isJPG && !isPNG) {
    ElMessage.error('头像图片只能是 JPG 或 PNG 格式!');
    return false;
  }
  if (!isLt2M) {
    ElMessage.error('头像图片大小不能超过 2MB!');
    return false;
  }

  // 拦截默认上传，使用自定义上传
  uploading.value = true;
  handleCustomUpload(file);
  return false;
};

// 自定义上传处理
const handleCustomUpload = async (file: File) => {
  try {
    const result = await uploadAvatar(file);
    handleAvatarSuccess(result);
  } catch (error) {
    handleAvatarError(error);
  } finally {
    uploading.value = false;
  }
};

// 上传成功处理
const handleAvatarSuccess = (result: AvatarUploadResult) => {
  avatarUrl.value = result.url;
  emit('success', result);
  ElMessage.success('头像上传成功');
};

// 上传失败处理
const handleAvatarError = (error: any) => {
  emit('error', error);
  ElMessage.error(error.msg || '头像上传失败');
};
</script>

<style scoped>
.avatar-upload {
  text-align: center;
}

.avatar-uploader {
  display: inline-block;
}

.avatar-container {
  position: relative;
  display: inline-block;
  cursor: pointer;
  border-radius: 50%;
  overflow: hidden;
  transition: var(--transition-all);
}

.avatar-container:hover {
  transform: scale(1.05);
}

.avatar-container:hover .avatar-overlay {
  opacity: 1;
}

.avatar {
  display: block;
  border: 2px solid var(--border-color);
}

.avatar-placeholder {
  background-color: var(--bg-color-secondary);
  color: var(--text-color-secondary);
}

.avatar-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: var(--transition-all);
  border-radius: 50%;
}

.avatar-overlay .el-icon {
  font-size: 24px;
  color: white;
}

.upload-tips {
  margin-top: 8px;
}

.upload-tips p {
  font-size: 12px;
  color: var(--text-color-secondary);
  margin: 0;
}

:deep(.el-upload) {
  border: none;
  border-radius: 50%;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--transition-all);
}

:deep(.el-upload:hover) {
  border-color: var(--primary-color);
}

.is-loading {
  animation: rotating 2s linear infinite;
}

@keyframes rotating {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>