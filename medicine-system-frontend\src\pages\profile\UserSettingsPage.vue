<template>
  <div class="user-settings-page">
    <div class="page-header">
      <el-button
        type="text"
        @click="$router.back()"
        class="back-button"
        :icon="ArrowLeft"
      >
        返回
      </el-button>
      <h1 class="page-title">偏好设置</h1>
      <p class="page-description">个性化您的使用体验</p>
    </div>

    <div class="settings-content">
      <el-row :gutter="24">
        <el-col :xs="24" :lg="16">
          <!-- 外观设置 -->
          <el-card class="settings-card">
            <template #header>
              <div class="card-header">
                <el-icon class="header-icon"><Sunny /></el-icon>
                <span>外观设置</span>
              </div>
            </template>

            <div class="setting-group">
              <div class="setting-item">
                <div class="setting-info">
                  <h4>主题模式</h4>
                  <p>选择您喜欢的界面主题</p>
                </div>
                <div class="setting-control">
                  <el-radio-group v-model="settingsForm.theme" @change="handleThemeChange">
                    <el-radio-button label="light">
                      <el-icon><Sunny /></el-icon>
                      浅色
                    </el-radio-button>
                    <el-radio-button label="dark">
                      <el-icon><Moon /></el-icon>
                      深色
                    </el-radio-button>
                    <el-radio-button label="auto">
                      <el-icon><Monitor /></el-icon>
                      跟随系统
                    </el-radio-button>
                  </el-radio-group>
                </div>
              </div>

              <el-divider />

              <div class="setting-item">
                <div class="setting-info">
                  <h4>语言设置</h4>
                  <p>选择界面显示语言</p>
                </div>
                <div class="setting-control">
                  <el-select v-model="settingsForm.language" @change="handleLanguageChange">
                    <el-option label="简体中文" value="zh-CN" />
                    <el-option label="English" value="en-US" />
                  </el-select>
                </div>
              </div>
            </div>
          </el-card>

          <!-- 布局设置 -->
          <el-card class="settings-card">
            <template #header>
              <div class="card-header">
                <el-icon class="header-icon"><Grid /></el-icon>
                <span>布局设置</span>
              </div>
            </template>

            <div class="setting-group">
              <div class="setting-item">
                <div class="setting-info">
                  <h4>侧边栏</h4>
                  <p>默认折叠侧边栏</p>
                </div>
                <div class="setting-control">
                  <el-switch
                    v-model="settingsForm.layout.sidebarCollapsed"
                    @change="handleLayoutChange"
                  />
                </div>
              </div>

              <el-divider />

              <div class="setting-item">
                <div class="setting-info">
                  <h4>面包屑导航</h4>
                  <p>显示页面路径导航</p>
                </div>
                <div class="setting-control">
                  <el-switch
                    v-model="settingsForm.layout.showBreadcrumb"
                    @change="handleLayoutChange"
                  />
                </div>
              </div>

              <el-divider />

              <div class="setting-item">
                <div class="setting-info">
                  <h4>标签页</h4>
                  <p>显示页面标签页</p>
                </div>
                <div class="setting-control">
                  <el-switch
                    v-model="settingsForm.layout.showTabs"
                    @change="handleLayoutChange"
                  />
                </div>
              </div>
            </div>
          </el-card>

          <!-- 通知设置 -->
          <el-card class="settings-card">
            <template #header>
              <div class="card-header">
                <el-icon class="header-icon"><Bell /></el-icon>
                <span>通知设置</span>
              </div>
            </template>

            <div class="setting-group">
              <div class="setting-item">
                <div class="setting-info">
                  <h4>邮件通知</h4>
                  <p>接收重要事件的邮件通知</p>
                </div>
                <div class="setting-control">
                  <el-switch
                    v-model="settingsForm.notifications.email"
                    @change="handleNotificationChange"
                  />
                </div>
              </div>

              <el-divider />

              <div class="setting-item">
                <div class="setting-info">
                  <h4>系统通知</h4>
                  <p>接收系统消息和提醒</p>
                </div>
                <div class="setting-control">
                  <el-switch
                    v-model="settingsForm.notifications.system"
                    @change="handleNotificationChange"
                  />
                </div>
              </div>

              <el-divider />

              <div class="setting-item">
                <div class="setting-info">
                  <h4>库存预警</h4>
                  <p>药品库存不足时发送通知</p>
                </div>
                <div class="setting-control">
                  <el-switch
                    v-model="settingsForm.notifications.lowStock"
                    @change="handleNotificationChange"
                  />
                </div>
              </div>

              <el-divider />

              <div class="setting-item">
                <div class="setting-info">
                  <h4>过期提醒</h4>
                  <p>药品即将过期时发送通知</p>
                </div>
                <div class="setting-control">
                  <el-switch
                    v-model="settingsForm.notifications.expiring"
                    @change="handleNotificationChange"
                  />
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 右侧预览 -->
        <el-col :xs="24" :lg="8">
          <el-card class="preview-card" v-sticky="{ offset: 80 }">
            <template #header>
              <div class="card-header">
                <el-icon class="header-icon"><View /></el-icon>
                <span>设置预览</span>
              </div>
            </template>

            <div class="preview-content">
              <div class="preview-item">
                <h4>当前主题</h4>
                <div class="theme-preview" :class="settingsForm.theme">
                  <div class="preview-header"></div>
                  <div class="preview-sidebar" :class="{ collapsed: settingsForm.layout.sidebarCollapsed }"></div>
                  <div class="preview-main">
                    <div v-if="settingsForm.layout.showBreadcrumb" class="preview-breadcrumb"></div>
                    <div v-if="settingsForm.layout.showTabs" class="preview-tabs"></div>
                    <div class="preview-content-area"></div>
                  </div>
                </div>
              </div>

              <div class="preview-item">
                <h4>通知状态</h4>
                <div class="notification-status">
                  <div class="status-item" :class="{ active: settingsForm.notifications.email }">
                    <el-icon><Message /></el-icon>
                    <span>邮件通知</span>
                  </div>
                  <div class="status-item" :class="{ active: settingsForm.notifications.system }">
                    <el-icon><Bell /></el-icon>
                    <span>系统通知</span>
                  </div>
                  <div class="status-item" :class="{ active: settingsForm.notifications.lowStock }">
                    <el-icon><Warning /></el-icon>
                    <span>库存预警</span>
                  </div>
                  <div class="status-item" :class="{ active: settingsForm.notifications.expiring }">
                    <el-icon><Clock /></el-icon>
                    <span>过期提醒</span>
                  </div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 操作按钮 -->
      <div class="settings-actions">
        <el-button size="large" @click="resetSettings">恢复默认</el-button>
        <el-button
          type="primary"
          size="large"
          @click="saveSettings"
          :loading="saving"
        >
          保存设置
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
  ArrowLeft,
  Sunny,
  Moon,
  Monitor,
  Grid,
  Bell,
  View,
  Message,
  Warning,
  Clock
} from '@element-plus/icons-vue';
import { useAuthStore } from '@/store/auth';
import type { UserSettings } from '@/types';

const authStore = useAuthStore();

// 响应式数据
const saving = ref(false);
const originalSettings = ref<UserSettings | null>(null);

// 表单数据
const settingsForm = reactive<UserSettings>({
  theme: 'light',
  language: 'zh-CN',
  notifications: {
    email: true,
    system: true,
    lowStock: true,
    expiring: true
  },
  layout: {
    sidebarCollapsed: false,
    showBreadcrumb: true,
    showTabs: true
  }
});

// 获取用户设置
const getUserSettings = async () => {
  try {
    const settings = await authStore.getUserSettingsAction();
    Object.assign(settingsForm, settings);
    originalSettings.value = { ...settings };
  } catch (error) {
    console.error('获取用户设置失败:', error);
  }
};

// 主题变更处理
const handleThemeChange = (theme: string) => {
  authStore.applyTheme(theme);
};

// 语言变更处理
const handleLanguageChange = (language: string) => {
  ElMessage.info('语言设置将在下次登录时生效');
};

// 布局变更处理
const handleLayoutChange = () => {
  ElMessage.info('布局设置将在页面刷新后生效');
};

// 通知变更处理
const handleNotificationChange = () => {
  // 可以在这里添加实时通知设置更新逻辑
};

// 保存设置
const saveSettings = async () => {
  try {
    saving.value = true;
    await authStore.updateUserSettingsAction(settingsForm);
    originalSettings.value = { ...settingsForm };
  } catch (error) {
    console.error('保存设置失败:', error);
  } finally {
    saving.value = false;
  }
};

// 恢复默认设置
const resetSettings = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要恢复默认设置吗？这将清除您的所有个性化配置。',
      '恢复默认设置',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );

    const defaultSettings: UserSettings = {
      theme: 'light',
      language: 'zh-CN',
      notifications: {
        email: true,
        system: true,
        lowStock: true,
        expiring: true
      },
      layout: {
        sidebarCollapsed: false,
        showBreadcrumb: true,
        showTabs: true
      }
    };

    Object.assign(settingsForm, defaultSettings);
    authStore.applyTheme(defaultSettings.theme);
    ElMessage.success('已恢复默认设置');
  } catch {
    // 用户取消操作
  }
};

// 组件挂载时获取设置
onMounted(() => {
  getUserSettings();
});
</script>

<style scoped>
.user-settings-page {
  padding: 24px;
  background-color: var(--bg-color-page);
  min-height: calc(100vh - 60px);
}

.page-header {
  margin-bottom: 32px;
  text-align: center;
  position: relative;
}

.back-button {
  position: absolute;
  left: 0;
  top: 0;
  font-size: 14px;
  color: var(--text-color-secondary);
}

.back-button:hover {
  color: var(--primary-color);
}

.page-title {
  font-size: 28px;
  font-weight: 600;
  color: var(--text-color-primary);
  margin: 0 0 8px 0;
}

.page-description {
  color: var(--text-color-secondary);
  margin: 0;
  font-size: 16px;
}

.settings-content {
  max-width: 1200px;
  margin: 0 auto;
}

.settings-card {
  margin-bottom: 24px;
}

.card-header {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: 600;
  color: var(--text-color-primary);
}

.header-icon {
  margin-right: 8px;
  font-size: 20px;
  color: var(--primary-color);
}

.setting-group {
  padding: 20px 0;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
}

.setting-info h4 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 500;
  color: var(--text-color-primary);
}

.setting-info p {
  margin: 0;
  font-size: 14px;
  color: var(--text-color-secondary);
}

.setting-control {
  flex-shrink: 0;
}

.preview-card {
  position: sticky;
  top: 80px;
}

.preview-content {
  padding: 20px 0;
}

.preview-item {
  margin-bottom: 24px;
}

.preview-item:last-child {
  margin-bottom: 0;
}

.preview-item h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--text-color-primary);
}

.theme-preview {
  width: 100%;
  height: 120px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-base);
  position: relative;
  overflow: hidden;
  background-color: var(--bg-color);
}

.theme-preview.dark {
  background-color: #1a1a1a;
  border-color: #333;
}

.preview-header {
  height: 20px;
  background-color: var(--primary-color);
}

.preview-sidebar {
  position: absolute;
  left: 0;
  top: 20px;
  bottom: 0;
  width: 40px;
  background-color: var(--bg-color-secondary);
  transition: var(--transition-all);
}

.preview-sidebar.collapsed {
  width: 20px;
}

.theme-preview.dark .preview-sidebar {
  background-color: #2a2a2a;
}

.preview-main {
  margin-left: 40px;
  padding: 8px;
  height: calc(100% - 20px);
}

.preview-sidebar.collapsed + .preview-main {
  margin-left: 20px;
}

.preview-breadcrumb {
  height: 8px;
  background-color: var(--border-color);
  border-radius: 2px;
  margin-bottom: 4px;
  width: 60%;
}

.preview-tabs {
  height: 12px;
  background-color: var(--bg-color-secondary);
  border-radius: 2px;
  margin-bottom: 4px;
}

.preview-content-area {
  flex: 1;
  background-color: var(--bg-color-tertiary);
  border-radius: 2px;
  height: calc(100% - 32px);
}

.theme-preview.dark .preview-breadcrumb {
  background-color: #444;
}

.theme-preview.dark .preview-tabs {
  background-color: #333;
}

.theme-preview.dark .preview-content-area {
  background-color: #2a2a2a;
}

.notification-status {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.status-item {
  display: flex;
  align-items: center;
  padding: 8px;
  border-radius: var(--border-radius-small);
  background-color: var(--bg-color-secondary);
  color: var(--text-color-secondary);
  transition: var(--transition-all);
}

.status-item.active {
  background-color: var(--primary-color-light);
  color: var(--primary-color);
}

.status-item .el-icon {
  margin-right: 4px;
  font-size: 14px;
}

.status-item span {
  font-size: 12px;
}

.settings-actions {
  text-align: center;
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid var(--border-color);
}

.settings-actions .el-button {
  min-width: 120px;
  margin: 0 8px;
}

:deep(.el-radio-button__inner) {
  display: flex;
  align-items: center;
  gap: 4px;
}

:deep(.el-divider--horizontal) {
  margin: 16px 0;
}

@media (max-width: 768px) {
  .user-settings-page {
    padding: 16px;
  }
  
  .page-header {
    margin-bottom: 24px;
  }
  
  .page-title {
    font-size: 24px;
  }
  
  .setting-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .setting-control {
    width: 100%;
  }
  
  .notification-status {
    grid-template-columns: 1fr;
  }
  
  .settings-actions .el-button {
    min-width: 100px;
    margin: 4px;
  }
}
</style>