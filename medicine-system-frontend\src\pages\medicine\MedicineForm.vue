<template>
  <div class="medicine-form">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="100px"
      :disabled="mode === 'view'"
    >
      <el-form-item label="药品名称" prop="name">
        <el-input v-model="formData.name" placeholder="请输入药品名称" />
      </el-form-item>
      
      <el-form-item label="药品分类" prop="categoryId">
        <el-select v-model="formData.categoryId" placeholder="请选择药品分类" style="width: 100%">
          <el-option
            v-for="item in categoryOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      
      <el-form-item label="规格" prop="spec">
        <el-input v-model="formData.spec" placeholder="请输入规格" />
      </el-form-item>
      
      <el-form-item label="批号" prop="batchNo">
        <el-input v-model="formData.batchNo" placeholder="请输入批号" />
      </el-form-item>
      
      <el-form-item label="有效期" prop="expireDate">
        <el-date-picker
          v-model="formData.expireDate"
          type="date"
          placeholder="请选择有效期"
          style="width: 100%"
        />
      </el-form-item>
      
      <el-form-item label="单价" prop="price">
        <el-input-number
          v-model="formData.price"
          :precision="2"
          :step="0.1"
          :min="0"
          style="width: 100%"
        />
      </el-form-item>
      
      <el-form-item label="库存量" prop="stock">
        <el-input-number
          v-model="formData.stock"
          :precision="0"
          :step="1"
          :min="0"
          style="width: 100%"
        />
      </el-form-item>
      
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio :label="1">正常</el-radio>
          <el-radio :label="0">停用</el-radio>
        </el-radio-group>
      </el-form-item>
      
      <el-form-item>
        <el-button v-if="mode !== 'view'" type="primary" @click="handleSubmit">提交</el-button>
        <el-button @click="handleCancel">{{ mode === 'view' ? '返回' : '取消' }}</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import type { FormInstance, FormRules } from 'element-plus';
import { getMedicineDetail, addMedicine, updateMedicine, getMedicineCategoryList } from '@/api/modules/medicine';
import type { Medicine, MedicineCategory } from '@/types';

// 定义组件的props
const props = defineProps<{
  id?: number;
  mode?: 'add' | 'edit' | 'view';
}>();

// 定义组件的事件
const emit = defineEmits<{
  (e: 'success'): void;
  (e: 'cancel'): void;
}>();

// 表单引用
const formRef = ref<FormInstance>();

// 表单数据
const formData = reactive({
  name: '',
  categoryId: 0,
  spec: '',
  batchNo: '',
  expireDate: '',
  price: 0,
  stock: 0,
  status: 1
});

// 表单验证规则
const rules = reactive<FormRules>({
  name: [
    { required: true, message: '请输入药品名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  categoryId: [
    { required: true, message: '请选择药品分类', trigger: 'change' }
  ],
  spec: [
    { required: true, message: '请输入规格', trigger: 'blur' }
  ],
  batchNo: [
    { required: true, message: '请输入批号', trigger: 'blur' }
  ],
  expireDate: [
    { required: true, message: '请选择有效期', trigger: 'change' }
  ],
  price: [
    { required: true, message: '请输入单价', trigger: 'blur' }
  ],
  stock: [
    { required: true, message: '请输入库存量', trigger: 'blur' }
  ]
});

// 药品分类选项
const categoryOptions = ref<{ label: string; value: number }[]>([]);

// 获取药品分类列表
const fetchCategories = async () => {
  try {
    const categoryList = await getMedicineCategoryList();
    categoryOptions.value = categoryList.map(item => ({
      label: item.name,
      value: item.id
    }));
  } catch (error) {
    console.error('获取药品分类失败:', error);
  }
};

// 获取药品详情
const fetchMedicineDetail = async (id: number) => {
  try {
    const detail = await getMedicineDetail(id);
    Object.keys(detail).forEach(key => {
      if (key in formData) {
        formData[key as keyof Medicine] = detail[key as keyof Medicine];
      }
    });
  } catch (error: any) {
    ElMessage.error(error.msg || '获取药品详情失败');
  }
};

// 处理提交
const handleSubmit = async () => {
  if (!formRef.value) return;

  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        // 转换数据格式以匹配后端期望
        const submitData = {
          ...formData,
          status: formData.status.toString() // 将数字状态转换为字符串
        };

        if (props.mode === 'edit' && props.id) {
          await updateMedicine(props.id, submitData);
          ElMessage.success('更新成功');
        } else {
          await addMedicine(submitData);
          ElMessage.success('添加成功');
        }
        emit('success');
      } catch (error: any) {
        ElMessage.error(error.msg || '操作失败');
      }
    }
  });
};

// 处理取消
const handleCancel = () => {
  emit('cancel');
};

// 页面加载时获取数据
onMounted(async () => {
  await fetchCategories();
  
  if ((props.mode === 'edit' || props.mode === 'view') && props.id) {
    await fetchMedicineDetail(props.id);
  }
});
</script>

<style scoped>
.medicine-form {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}
</style>
