package com.example.medicine.service;

import com.example.medicine.dto.LoginRequest;
import com.example.medicine.dto.LoginResponse;
import com.example.medicine.dto.UserProfileDto;

public interface AuthService {
    LoginResponse login(LoginRequest loginRequest);
    void logout(String token);
    UserProfileDto getUserProfile(String token);
    UserProfileDto updateUserProfile(String token, UserProfileDto profileDto);
}
