<template>
  <div class="medicine-add-page">
    <div class="page-header">
      <div class="header-left">
        <el-button 
          type="text" 
          @click="handleBack"
          class="back-button"
        >
          <el-icon><ArrowLeft /></el-icon>
          返回药品列表
        </el-button>
        <h2>添加药品</h2>
      </div>
    </div>
    
    <div class="page-content">
      <el-card class="form-card">
        <template #header>
          <div class="card-header">
            <span>药品信息</span>
            <el-text type="info" size="small">请填写完整的药品信息</el-text>
          </div>
        </template>
        
        <MedicineForm
          mode="add"
          @success="handleSuccess"
          @cancel="handleCancel"
        />
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { ArrowLeft } from '@element-plus/icons-vue';
import MedicineForm from './MedicineForm.vue';

const router = useRouter();

// 处理添加成功
const handleSuccess = () => {
  ElMessage.success('药品添加成功！');
  // 跳转回药品列表页面
  router.push('/medicine');
};

// 处理取消操作
const handleCancel = () => {
  router.push('/medicine');
};

// 处理返回按钮
const handleBack = () => {
  router.push('/medicine');
};
</script>

<style scoped>
.medicine-add-page {
  padding: 20px;
  background-color: var(--bg-color);
  min-height: calc(100vh - 60px);
}

.page-header {
  margin-bottom: 20px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 8px 12px;
  color: var(--text-color-secondary);
  transition: all 0.3s ease;
}

.back-button:hover {
  color: var(--primary-color);
  background-color: var(--primary-color-light-9);
}

.page-header h2 {
  margin: 0;
  color: var(--text-color-primary);
  font-size: 24px;
  font-weight: 600;
}

.page-content {
  max-width: 800px;
}

.form-card {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  border: 1px solid var(--border-color-light);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header span {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-color-primary);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .medicine-add-page {
    padding: 16px;
  }
  
  .page-header h2 {
    font-size: 20px;
  }
  
  .page-content {
    max-width: 100%;
  }
  
  .header-left {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}
</style>
