// 通用响应结构
export interface ApiResponse<T = any> {
  code: number;
  msg: string;
  data: T;
}

// 分页参数
export interface PageParams {
  page: number;
  size: number;
  keyword?: string;
}

// 分页响应
export interface PageResult<T> {
  records: T[];
  total: number;
  current: number;
  size: number;
}

// 药品相关类型
export interface Medicine {
  id?: number;
  name: string;
  categoryId?: number;
  categoryName?: string;
  spec: string;
  batchNo: string;
  expireDate: string;
  price: number;
  stock: number;
  status: string;
  createTime?: string;
  updateTime?: string;
}

export interface MedicineCategory {
  id: number;
  name: string;
  description?: string;
  createTime?: string;
  updateTime?: string;
}

// 库存相关类型
export interface Inventory {
  id?: number;
  medicineId: number;
  medicineName?: string;
  warehouseId?: number;
  warehouseName?: string;
  quantity: number;
  lastUpdate?: string;
}

export interface InventoryRecord {
  id?: number;
  medicineId: number;
  medicineName?: string;
  type: 'IN' | 'OUT'; // 入库/出库
  quantity: number;
  beforeStock: number;
  afterStock: number;
  reason: string;
  operatorId: number;
  operatorName?: string;
  createTime?: string;
}

// 采购相关类型
export interface Purchase {
  id?: number;
  supplierId?: number;
  supplierName?: string;
  medicineId?: number;
  medicineName?: string;
  quantity: number;
  price: number;
  totalAmount?: number;
  purchaseDate: string;
  status: string;
  createTime?: string;
}

export interface Supplier {
  id?: number;
  name: string;
  contact: string;
  address: string;
  status?: number;
  createTime?: string;
}

// 销售相关类型
export interface Sale {
  id?: number;
  customerId?: number;
  customerName?: string;
  medicineId?: number;
  medicineName?: string;
  quantity: number;
  price: number;
  totalAmount?: number;
  saleDate: string;
  status: string;
  createTime?: string;
}

export interface Customer {
  id?: number;
  name: string;
  contact: string;
  address?: string;
  status?: number;
  createTime?: string;
}

// 用户相关类型
export interface User {
  id?: number;
  username: string;
  password?: string;
  roleId?: number;
  roleName?: string;
  status: string;
  createTime?: string;
  lastLoginTime?: string;
}

export interface Role {
  id?: number;
  roleName: string;
  permissions?: string[];
  description?: string;
  createTime?: string;
}

// 登录相关类型
export interface LoginForm {
  username: string;
  password: string;
}

// 注册相关类型
export interface RegisterForm {
  username: string;
  password: string;
  confirmPassword?: string;
  email: string;
  phone: string;
}

export interface LoginResult {
  token: string;
  user: User;
}

// 用户个人信息类型
export interface UserProfile {
  id?: number;
  username: string;
  email?: string;
  phone?: string;
  avatar?: string;
  realName?: string;
  department?: string;
  position?: string;
  roleName?: string;
  createTime?: string;
  lastLoginTime?: string;
}

// 修改密码表单类型
export interface ChangePasswordForm {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

// 用户偏好设置类型
export interface UserSettings {
  theme: 'light' | 'dark' | 'auto';
  language: 'zh-CN' | 'en-US';
  notifications: {
    email: boolean;
    system: boolean;
    lowStock: boolean;
    expiring: boolean;
  };
  layout: {
    sidebarCollapsed: boolean;
    showBreadcrumb: boolean;
    showTabs: boolean;
  };
}

// 头像上传响应类型
export interface AvatarUploadResult {
  url: string;
  filename: string;
}

// 统计相关类型
export interface DashboardStats {
  medicineCount: number;
  lowStockCount: number;
  expiringSoonCount: number;
  todaySales: number;
  monthSales: number;
  totalRevenue: number;
}

// 日志相关类型
export interface SystemLog {
  id?: number;
  userId: number;
  username?: string;
  action: string;
  module: string;
  detail?: string;
  ip?: string;
  createTime?: string;
}

// 表单验证规则类型
export interface FormRule {
  required?: boolean;
  message: string;
  trigger?: string | string[];
  min?: number;
  max?: number;
  pattern?: RegExp;
  validator?: (rule: any, value: any, callback: any) => void;
}

// 菜单项类型
export interface MenuItem {
  path: string;
  name: string;
  icon?: string;
  children?: MenuItem[];
  permission?: string;
}