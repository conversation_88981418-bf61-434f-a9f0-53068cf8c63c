package com.example.medicine.dto;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserProfileDto {
    private Long id;
    private String username;
    private String email;
    private String phone;
    private String avatar;
    private String realName;
    private String department;
    private String position;
    private String roleName;
    private Date createTime;
    private Date lastLoginTime;
}
