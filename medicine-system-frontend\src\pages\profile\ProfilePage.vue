<template>
  <div class="profile-page">
    <div class="page-header">
      <h1 class="page-title">个人信息</h1>
      <p class="page-description">管理您的个人资料和账户信息</p>
    </div>

    <div class="profile-content">
      <el-row :gutter="24">
        <!-- 左侧头像和基本信息 -->
        <el-col :xs="24" :sm="24" :md="8" :lg="6">
          <el-card class="profile-card">
            <div class="profile-avatar-section">
              <AvatarUpload
                v-model="profileForm.avatar"
                :size="120"
                @success="handleAvatarSuccess"
              />
              <h3 class="profile-username">{{ profileForm.username }}</h3>
              <p class="profile-role">{{ profileForm.roleName || '普通用户' }}</p>
              <div class="profile-stats">
                <div class="stat-item">
                  <span class="stat-label">注册时间</span>
                  <span class="stat-value">{{ formatDate(profileForm.createTime) }}</span>
                </div>
                <div class="stat-item" v-if="profileForm.lastLoginTime">
                  <span class="stat-label">最后登录</span>
                  <span class="stat-value">{{ formatDate(profileForm.lastLoginTime) }}</span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 右侧详细信息表单 -->
        <el-col :xs="24" :sm="24" :md="16" :lg="18">
          <el-card class="profile-form-card">
            <template #header>
              <div class="card-header">
                <span>详细信息</span>
                <el-button
                  v-if="!isEditing"
                  type="primary"
                  @click="startEdit"
                  :icon="Edit"
                >
                  编辑资料
                </el-button>
                <div v-else class="edit-actions">
                  <el-button @click="cancelEdit">取消</el-button>
                  <el-button
                    type="primary"
                    @click="saveProfile"
                    :loading="saving"
                    :icon="Check"
                  >
                    保存
                  </el-button>
                </div>
              </div>
            </template>

            <el-form
              ref="profileFormRef"
              :model="profileForm"
              :rules="profileRules"
              label-width="100px"
              class="profile-form"
            >
              <el-row :gutter="24">
                <el-col :xs="24" :sm="12">
                  <el-form-item label="用户名" prop="username">
                    <el-input
                      v-model="profileForm.username"
                      :disabled="!isEditing"
                      placeholder="请输入用户名"
                    />
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="12">
                  <el-form-item label="真实姓名" prop="realName">
                    <el-input
                      v-model="profileForm.realName"
                      :disabled="!isEditing"
                      placeholder="请输入真实姓名"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="24">
                <el-col :xs="24" :sm="12">
                  <el-form-item label="邮箱地址" prop="email">
                    <el-input
                      v-model="profileForm.email"
                      :disabled="!isEditing"
                      placeholder="请输入邮箱地址"
                      type="email"
                    />
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="12">
                  <el-form-item label="手机号码" prop="phone">
                    <el-input
                      v-model="profileForm.phone"
                      :disabled="!isEditing"
                      placeholder="请输入手机号码"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="24">
                <el-col :xs="24" :sm="12">
                  <el-form-item label="部门" prop="department">
                    <el-input
                      v-model="profileForm.department"
                      :disabled="!isEditing"
                      placeholder="请输入所属部门"
                    />
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="12">
                  <el-form-item label="职位" prop="position">
                    <el-input
                      v-model="profileForm.position"
                      :disabled="!isEditing"
                      placeholder="请输入职位"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-form-item label="角色">
                <el-tag type="info" size="large">{{ profileForm.roleName || '普通用户' }}</el-tag>
              </el-form-item>
            </el-form>
          </el-card>
        </el-col>
      </el-row>

      <!-- 快捷操作 -->
      <el-row :gutter="24" class="quick-actions">
        <el-col :xs="24" :sm="8">
          <el-card class="action-card" @click="$router.push('/change-password')">
            <div class="action-content">
              <el-icon class="action-icon"><Key /></el-icon>
              <div class="action-text">
                <h4>修改密码</h4>
                <p>更改您的登录密码</p>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="8">
          <el-card class="action-card" @click="$router.push('/user-settings')">
            <div class="action-content">
              <el-icon class="action-icon"><Setting /></el-icon>
              <div class="action-text">
                <h4>偏好设置</h4>
                <p>个性化您的使用体验</p>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="8">
          <el-card class="action-card">
            <div class="action-content">
              <el-icon class="action-icon"><Document /></el-icon>
              <div class="action-text">
                <h4>操作日志</h4>
                <p>查看您的操作记录</p>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox, type FormInstance } from 'element-plus';
import { Edit, Check, Key, Setting, Document } from '@element-plus/icons-vue';
import { useAuthStore } from '@/store/auth';
import AvatarUpload from '@/components/common/AvatarUpload.vue';
import type { UserProfile, AvatarUploadResult } from '@/types';

const authStore = useAuthStore();

// 响应式数据
const profileFormRef = ref<FormInstance>();
const isEditing = ref(false);
const saving = ref(false);
const originalProfile = ref<UserProfile | null>(null);

// 表单数据
const profileForm = reactive<UserProfile>({
  username: '',
  email: '',
  phone: '',
  avatar: '',
  realName: '',
  department: '',
  position: '',
  roleName: '',
  createTime: '',
  lastLoginTime: ''
});

// 表单验证规则
const profileRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  realName: [
    { max: 50, message: '真实姓名不能超过50个字符', trigger: 'blur' }
  ],
  department: [
    { max: 100, message: '部门名称不能超过100个字符', trigger: 'blur' }
  ],
  position: [
    { max: 100, message: '职位名称不能超过100个字符', trigger: 'blur' }
  ]
};

// 获取用户信息
const getUserProfile = async () => {
  try {
    const profile = await authStore.getUserProfileAction();
    Object.assign(profileForm, profile);
    originalProfile.value = { ...profile };
  } catch (error) {
    console.error('获取用户信息失败:', error);
  }
};

// 开始编辑
const startEdit = () => {
  isEditing.value = true;
  originalProfile.value = { ...profileForm };
};

// 取消编辑
const cancelEdit = () => {
  if (originalProfile.value) {
    Object.assign(profileForm, originalProfile.value);
  }
  isEditing.value = false;
};

// 保存个人信息
const saveProfile = async () => {
  if (!profileFormRef.value) return;

  try {
    await profileFormRef.value.validate();
    saving.value = true;

    await authStore.updateUserProfileAction(profileForm);
    isEditing.value = false;
    originalProfile.value = { ...profileForm };
  } catch (error) {
    console.error('保存个人信息失败:', error);
  } finally {
    saving.value = false;
  }
};

// 头像上传成功处理
const handleAvatarSuccess = (result: AvatarUploadResult) => {
  profileForm.avatar = result.url;
  if (!isEditing.value) {
    // 如果不在编辑模式，直接保存头像
    authStore.updateUserProfileAction({ avatar: result.url });
  }
};

// 格式化日期
const formatDate = (dateString?: string) => {
  if (!dateString) return '未知';
  return new Date(dateString).toLocaleString('zh-CN');
};

// 组件挂载时获取用户信息
onMounted(() => {
  getUserProfile();
});
</script>

<style scoped>
.profile-page {
  padding: 24px;
  background-color: var(--bg-color-page);
  min-height: calc(100vh - 60px);
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-color-primary);
  margin: 0 0 8px 0;
}

.page-description {
  color: var(--text-color-secondary);
  margin: 0;
}

.profile-content {
  max-width: 1200px;
}

.profile-card {
  margin-bottom: 24px;
}

.profile-avatar-section {
  text-align: center;
  padding: 20px 0;
}

.profile-username {
  margin: 16px 0 8px 0;
  font-size: 20px;
  font-weight: 600;
  color: var(--text-color-primary);
}

.profile-role {
  color: var(--text-color-secondary);
  margin: 0 0 20px 0;
}

.profile-stats {
  border-top: 1px solid var(--border-color);
  padding-top: 20px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.stat-item:last-child {
  margin-bottom: 0;
}

.stat-label {
  color: var(--text-color-secondary);
  font-size: 14px;
}

.stat-value {
  color: var(--text-color-primary);
  font-size: 14px;
  font-weight: 500;
}

.profile-form-card {
  margin-bottom: 24px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.edit-actions {
  display: flex;
  gap: 12px;
}

.profile-form {
  padding: 20px 0;
}

.quick-actions {
  margin-top: 24px;
}

.action-card {
  cursor: pointer;
  transition: var(--transition-all);
  height: 100%;
}

.action-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--box-shadow-hover);
}

.action-content {
  display: flex;
  align-items: center;
  padding: 16px 0;
}

.action-icon {
  font-size: 32px;
  color: var(--primary-color);
  margin-right: 16px;
}

.action-text h4 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-color-primary);
}

.action-text p {
  margin: 0;
  font-size: 14px;
  color: var(--text-color-secondary);
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: var(--text-color-primary);
}

:deep(.el-input.is-disabled .el-input__inner) {
  background-color: var(--bg-color-secondary);
  border-color: var(--border-color);
  color: var(--text-color-primary);
}

:deep(.el-card__body) {
  padding: 20px;
}

@media (max-width: 768px) {
  .profile-page {
    padding: 16px;
  }
  
  .quick-actions .el-col {
    margin-bottom: 16px;
  }
  
  .action-content {
    padding: 12px 0;
  }
  
  .action-icon {
    font-size: 28px;
    margin-right: 12px;
  }
}
</style>