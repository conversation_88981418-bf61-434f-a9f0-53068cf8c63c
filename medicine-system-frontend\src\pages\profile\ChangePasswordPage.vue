<template>
  <div class="change-password-page">
    <div class="page-header">
      <el-button
        type="text"
        @click="$router.back()"
        class="back-button"
        :icon="ArrowLeft"
      >
        返回
      </el-button>
      <h1 class="page-title">修改密码</h1>
      <p class="page-description">为了您的账户安全，请定期更换密码</p>
    </div>

    <div class="password-content">
      <el-row justify="center">
        <el-col :xs="24" :sm="20" :md="16" :lg="12" :xl="10">
          <el-card class="password-card">
            <template #header>
              <div class="card-header">
                <el-icon class="header-icon"><Lock /></el-icon>
                <span>密码修改</span>
              </div>
            </template>

            <el-form
              ref="passwordFormRef"
              :model="passwordForm"
              :rules="passwordRules"
              label-width="120px"
              class="password-form"
              @submit.prevent="changePassword"
            >
              <el-form-item label="当前密码" prop="currentPassword">
                <el-input
                  v-model="passwordForm.currentPassword"
                  type="password"
                  placeholder="请输入当前密码"
                  show-password
                  :prefix-icon="Lock"
                  size="large"
                />
              </el-form-item>

              <el-form-item label="新密码" prop="newPassword">
                <el-input
                  v-model="passwordForm.newPassword"
                  type="password"
                  placeholder="请输入新密码"
                  show-password
                  :prefix-icon="Key"
                  size="large"
                  @input="checkPasswordStrength"
                />
                <div class="password-strength" v-if="passwordForm.newPassword">
                  <div class="strength-bar">
                    <div
                      class="strength-fill"
                      :class="strengthClass"
                      :style="{ width: strengthWidth }"
                    ></div>
                  </div>
                  <span class="strength-text" :class="strengthClass">
                    {{ strengthText }}
                  </span>
                </div>
              </el-form-item>

              <el-form-item label="确认新密码" prop="confirmPassword">
                <el-input
                  v-model="passwordForm.confirmPassword"
                  type="password"
                  placeholder="请再次输入新密码"
                  show-password
                  :prefix-icon="Key"
                  size="large"
                />
              </el-form-item>

              <div class="password-tips">
                <h4>密码安全建议：</h4>
                <ul>
                  <li :class="{ active: hasMinLength }">
                    <el-icon><Check v-if="hasMinLength" /><Close v-else /></el-icon>
                    至少8个字符
                  </li>
                  <li :class="{ active: hasUpperCase }">
                    <el-icon><Check v-if="hasUpperCase" /><Close v-else /></el-icon>
                    包含大写字母
                  </li>
                  <li :class="{ active: hasLowerCase }">
                    <el-icon><Check v-if="hasLowerCase" /><Close v-else /></el-icon>
                    包含小写字母
                  </li>
                  <li :class="{ active: hasNumber }">
                    <el-icon><Check v-if="hasNumber" /><Close v-else /></el-icon>
                    包含数字
                  </li>
                  <li :class="{ active: hasSpecialChar }">
                    <el-icon><Check v-if="hasSpecialChar" /><Close v-else /></el-icon>
                    包含特殊字符
                  </li>
                </ul>
              </div>

              <el-form-item class="form-actions">
                <el-button size="large" @click="resetForm">重置</el-button>
                <el-button
                  type="primary"
                  size="large"
                  @click="changePassword"
                  :loading="changing"
                  :disabled="!isFormValid"
                >
                  修改密码
                </el-button>
              </el-form-item>
            </el-form>
          </el-card>
        </el-col>
      </el-row>

      <!-- 安全提示 -->
      <el-row justify="center" class="security-tips">
        <el-col :xs="24" :sm="20" :md="16" :lg="12" :xl="10">
          <el-alert
            title="安全提示"
            type="info"
            :closable="false"
            show-icon
          >
            <template #default>
              <ul class="tips-list">
                <li>修改密码后需要重新登录</li>
                <li>请不要使用过于简单的密码</li>
                <li>建议定期更换密码以保证账户安全</li>
                <li>请妥善保管您的密码，不要告诉他人</li>
              </ul>
            </template>
          </el-alert>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage, ElMessageBox, type FormInstance } from 'element-plus';
import { ArrowLeft, Lock, Key, Check, Close } from '@element-plus/icons-vue';
import { useAuthStore } from '@/store/auth';
import type { ChangePasswordForm } from '@/types';

const router = useRouter();
const authStore = useAuthStore();

// 响应式数据
const passwordFormRef = ref<FormInstance>();
const changing = ref(false);

// 表单数据
const passwordForm = reactive<ChangePasswordForm>({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
});

// 密码强度相关
const passwordStrength = ref(0);

// 密码验证规则
const validateCurrentPassword = (rule: any, value: string, callback: any) => {
  if (!value) {
    callback(new Error('请输入当前密码'));
  } else if (value.length < 6) {
    callback(new Error('密码长度不能少于6位'));
  } else {
    callback();
  }
};

const validateNewPassword = (rule: any, value: string, callback: any) => {
  if (!value) {
    callback(new Error('请输入新密码'));
  } else if (value.length < 8) {
    callback(new Error('新密码长度不能少于8位'));
  } else if (value === passwordForm.currentPassword) {
    callback(new Error('新密码不能与当前密码相同'));
  } else {
    // 触发确认密码验证
    if (passwordForm.confirmPassword) {
      passwordFormRef.value?.validateField('confirmPassword');
    }
    callback();
  }
};

const validateConfirmPassword = (rule: any, value: string, callback: any) => {
  if (!value) {
    callback(new Error('请确认新密码'));
  } else if (value !== passwordForm.newPassword) {
    callback(new Error('两次输入的密码不一致'));
  } else {
    callback();
  }
};

const passwordRules = {
  currentPassword: [{ validator: validateCurrentPassword, trigger: 'blur' }],
  newPassword: [{ validator: validateNewPassword, trigger: 'blur' }],
  confirmPassword: [{ validator: validateConfirmPassword, trigger: 'blur' }]
};

// 密码强度检查
const checkPasswordStrength = () => {
  const password = passwordForm.newPassword;
  let strength = 0;

  if (password.length >= 8) strength += 1;
  if (/[A-Z]/.test(password)) strength += 1;
  if (/[a-z]/.test(password)) strength += 1;
  if (/\d/.test(password)) strength += 1;
  if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) strength += 1;

  passwordStrength.value = strength;
};

// 计算属性
const hasMinLength = computed(() => passwordForm.newPassword.length >= 8);
const hasUpperCase = computed(() => /[A-Z]/.test(passwordForm.newPassword));
const hasLowerCase = computed(() => /[a-z]/.test(passwordForm.newPassword));
const hasNumber = computed(() => /\d/.test(passwordForm.newPassword));
const hasSpecialChar = computed(() => /[!@#$%^&*(),.?":{}|<>]/.test(passwordForm.newPassword));

const strengthClass = computed(() => {
  const strength = passwordStrength.value;
  if (strength <= 2) return 'weak';
  if (strength <= 3) return 'medium';
  return 'strong';
});

const strengthText = computed(() => {
  const strength = passwordStrength.value;
  if (strength <= 2) return '弱';
  if (strength <= 3) return '中等';
  return '强';
});

const strengthWidth = computed(() => {
  return `${(passwordStrength.value / 5) * 100}%`;
});

const isFormValid = computed(() => {
  return passwordForm.currentPassword &&
         passwordForm.newPassword &&
         passwordForm.confirmPassword &&
         passwordForm.newPassword === passwordForm.confirmPassword &&
         passwordForm.newPassword !== passwordForm.currentPassword &&
         passwordStrength.value >= 3;
});

// 修改密码
const changePassword = async () => {
  if (!passwordFormRef.value) return;

  try {
    await passwordFormRef.value.validate();

    await ElMessageBox.confirm(
      '修改密码后需要重新登录，确定要继续吗？',
      '确认修改',
      {
        confirmButtonText: '确定修改',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );

    changing.value = true;
    await authStore.changePasswordAction(passwordForm);
    
    // 修改成功后跳转到登录页
    router.push('/login');
  } catch (error) {
    if (error !== 'cancel') {
      console.error('修改密码失败:', error);
    }
  } finally {
    changing.value = false;
  }
};

// 重置表单
const resetForm = () => {
  passwordFormRef.value?.resetFields();
  passwordStrength.value = 0;
};
</script>

<style scoped>
.change-password-page {
  padding: 24px;
  background-color: var(--bg-color-page);
  min-height: calc(100vh - 60px);
}

.page-header {
  margin-bottom: 32px;
  text-align: center;
}

.back-button {
  position: absolute;
  left: 0;
  top: 0;
  font-size: 14px;
  color: var(--text-color-secondary);
}

.back-button:hover {
  color: var(--primary-color);
}

.page-title {
  font-size: 28px;
  font-weight: 600;
  color: var(--text-color-primary);
  margin: 0 0 8px 0;
}

.page-description {
  color: var(--text-color-secondary);
  margin: 0;
  font-size: 16px;
}

.password-content {
  max-width: 1200px;
  margin: 0 auto;
}

.password-card {
  margin-bottom: 24px;
}

.card-header {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: 600;
  color: var(--text-color-primary);
}

.header-icon {
  margin-right: 8px;
  font-size: 20px;
  color: var(--primary-color);
}

.password-form {
  padding: 20px 0;
}

.password-strength {
  margin-top: 8px;
}

.strength-bar {
  width: 100%;
  height: 4px;
  background-color: var(--border-color);
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: 4px;
}

.strength-fill {
  height: 100%;
  transition: var(--transition-all);
  border-radius: 2px;
}

.strength-fill.weak {
  background-color: var(--danger-color);
}

.strength-fill.medium {
  background-color: var(--warning-color);
}

.strength-fill.strong {
  background-color: var(--success-color);
}

.strength-text {
  font-size: 12px;
  font-weight: 500;
}

.strength-text.weak {
  color: var(--danger-color);
}

.strength-text.medium {
  color: var(--warning-color);
}

.strength-text.strong {
  color: var(--success-color);
}

.password-tips {
  background-color: var(--bg-color-secondary);
  border-radius: var(--border-radius-base);
  padding: 16px;
  margin: 20px 0;
}

.password-tips h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--text-color-primary);
}

.password-tips ul {
  margin: 0;
  padding: 0;
  list-style: none;
}

.password-tips li {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 13px;
  color: var(--text-color-secondary);
  transition: var(--transition-all);
}

.password-tips li:last-child {
  margin-bottom: 0;
}

.password-tips li.active {
  color: var(--success-color);
}

.password-tips li .el-icon {
  margin-right: 8px;
  font-size: 14px;
}

.form-actions {
  margin-top: 32px;
  text-align: center;
}

.form-actions .el-button {
  min-width: 120px;
  margin: 0 8px;
}

.security-tips {
  margin-top: 32px;
}

.tips-list {
  margin: 0;
  padding-left: 20px;
}

.tips-list li {
  margin-bottom: 8px;
  color: var(--text-color-secondary);
  font-size: 14px;
}

.tips-list li:last-child {
  margin-bottom: 0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: var(--text-color-primary);
}

:deep(.el-input__inner) {
  font-size: 16px;
}

:deep(.el-alert__content) {
  padding-left: 0;
}

@media (max-width: 768px) {
  .change-password-page {
    padding: 16px;
  }
  
  .page-header {
    margin-bottom: 24px;
  }
  
  .page-title {
    font-size: 24px;
  }
  
  .page-description {
    font-size: 14px;
  }
  
  .password-form {
    padding: 16px 0;
  }
  
  .form-actions .el-button {
    min-width: 100px;
    margin: 0 4px;
  }
}
</style>