package com.example.medicine.entity;

import lombok.Data;

import javax.persistence.*;
import java.util.Date;

@Data
@Entity
@Table(name = "user")
public class User {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String username;
    private String password;
    private Long roleId;
    private String status;
    private Date createTime;
    private Date lastLoginTime;

    // Profile related fields
    private String email;
    private String phone;
    private String avatar;
    private String realName;
    private String department;
    private String position;
}