package com.example.medicine.dto;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserSettingsDto {
    private String theme;
    private String language;
    private NotificationSettings notifications;
    private LayoutSettings layout;
    
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class NotificationSettings {
        private Boolean email;
        private Boolean system;
        private Boolean lowStock;
        private Boolean expiring;
    }
    
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class LayoutSettings {
        private Boolean sidebarCollapsed;
        private Boolean showBreadcrumb;
        private Boolean showTabs;
    }
}
