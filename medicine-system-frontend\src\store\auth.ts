import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import type { User, LoginForm, UserProfile, ChangePasswordForm, UserSettings } from '../types';
import { login, getUserInfo, logout, getCurrentUserProfile, updateUserProfile, changePassword, getUserSettings, updateUserSettings } from '../api/modules/auth';
import { ElMessage } from 'element-plus';

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const token = ref<string>(localStorage.getItem('token') || '');
  const user = ref<User | null>(null);
  const permissions = ref<string[]>([]);
  const userProfile = ref<UserProfile | null>(null);
  const userSettings = ref<UserSettings>({
    theme: 'light',
    language: 'zh-CN',
    notifications: {
      email: true,
      system: true,
      lowStock: true,
      expiring: true
    },
    layout: {
      sidebarCollapsed: false,
      showBreadcrumb: true,
      showTabs: true
    }
  });

  // 计算属性
  const isLoggedIn = computed(() => !!token.value);
  const userRole = computed(() => user.value?.roleName || '');

  // 设置token
  const setToken = (newToken: string) => {
    token.value = newToken;
    localStorage.setItem('token', newToken);
  };

  // 清除token
  const clearToken = () => {
    token.value = '';
    localStorage.removeItem('token');
  };

  // 设置用户信息
  const setUser = (userInfo: User) => {
    user.value = userInfo;
  };

  // 设置权限
  const setPermissions = (perms: string[]) => {
    permissions.value = perms;
  };

  // 登录
  const loginAction = async (loginForm: LoginForm) => {
    try {
      const result = await login(loginForm);
      setToken(result.token);
      setUser(result.user);
      ElMessage.success('登录成功');
      return true;
    } catch (error: any) {
      ElMessage.error(error.msg || '登录失败');
      return false;
    }
  };

  // 获取用户信息
  const getUserInfoAction = async () => {
    try {
      const userInfo = await getUserInfo();
      setUser(userInfo);
      // 这里可以根据角色设置权限
      const rolePermissions = getRolePermissions(userInfo.roleName || '');
      setPermissions(rolePermissions);
      return userInfo;
    } catch (error) {
      clearToken();
      throw error;
    }
  };

  // 登出
  const logoutAction = async () => {
    try {
      await logout();
    } catch (error) {
      console.error('登出请求失败:', error);
    } finally {
      clearToken();
      user.value = null;
      permissions.value = [];
      ElMessage.success('已退出登录');
    }
  };

  // 获取用户详细信息
  const getUserProfileAction = async () => {
    try {
      const profile = await getCurrentUserProfile();
      userProfile.value = profile;
      return profile;
    } catch (error: any) {
      ElMessage.error(error.msg || '获取用户信息失败');
      throw error;
    }
  };

  // 更新用户信息
  const updateUserProfileAction = async (data: Partial<UserProfile>) => {
    try {
      const updatedProfile = await updateUserProfile(data);
      userProfile.value = updatedProfile;
      // 同时更新基本用户信息
      if (user.value) {
        user.value.username = updatedProfile.username;
      }
      ElMessage.success('个人信息更新成功');
      return updatedProfile;
    } catch (error: any) {
      ElMessage.error(error.msg || '更新个人信息失败');
      throw error;
    }
  };

  // 修改密码
  const changePasswordAction = async (data: ChangePasswordForm) => {
    try {
      await changePassword(data);
      ElMessage.success('密码修改成功，请重新登录');
      // 修改密码后需要重新登录
      await logoutAction();
      return true;
    } catch (error: any) {
      ElMessage.error(error.msg || '密码修改失败');
      throw error;
    }
  };

  // 获取用户偏好设置
  const getUserSettingsAction = async () => {
    try {
      const response = await getUserSettings();
      const settings = response.data || response;
      userSettings.value = settings;
      // 应用主题设置
      applyTheme(settings.theme);
      return settings;
    } catch (error: any) {
      console.error('获取用户设置失败:', error);
      // 使用默认设置
      return userSettings.value;
    }
  };

  // 更新用户偏好设置
  const updateUserSettingsAction = async (data: UserSettings) => {
    try {
      const response = await updateUserSettings(data);
      const updatedSettings = response.data || response;
      userSettings.value = updatedSettings;
      // 应用主题设置
      applyTheme(updatedSettings.theme);
      ElMessage.success('偏好设置保存成功');
      return updatedSettings;
    } catch (error: any) {
      ElMessage.error(error.msg || '保存偏好设置失败');
      throw error;
    }
  };

  // 应用主题设置
  const applyTheme = (theme: string) => {
    const html = document.documentElement;
    if (theme === 'dark') {
      html.classList.add('dark');
    } else if (theme === 'light') {
      html.classList.remove('dark');
    } else {
      // auto模式，根据系统偏好
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      if (prefersDark) {
        html.classList.add('dark');
      } else {
        html.classList.remove('dark');
      }
    }
  };

  // 检查权限
  const hasPermission = (permission: string) => {
    return permissions.value.includes(permission) || permissions.value.includes('*');
  };

  // 根据角色获取权限（示例实现）
  const getRolePermissions = (roleName: string): string[] => {
    const rolePermissionMap: Record<string, string[]> = {
      '超级管理员': ['*'],
      '管理员': [
        'medicine:view', 'medicine:add', 'medicine:edit', 'medicine:delete',
        'inventory:view', 'inventory:add', 'inventory:edit',
        'purchase:view', 'purchase:add', 'purchase:edit',
        'sale:view', 'sale:add', 'sale:edit',
        'user:view', 'user:add', 'user:edit',
        'report:view'
      ],
      '采购员': [
        'medicine:view',
        'inventory:view',
        'purchase:view', 'purchase:add', 'purchase:edit',
        'supplier:view', 'supplier:add', 'supplier:edit'
      ],
      '销售员': [
        'medicine:view',
        'inventory:view',
        'sale:view', 'sale:add', 'sale:edit',
        'customer:view', 'customer:add', 'customer:edit'
      ],
      '仓库管理员': [
        'medicine:view',
        'inventory:view', 'inventory:add', 'inventory:edit'
      ]
    };
    return rolePermissionMap[roleName] || [];
  };

  return {
    // 状态
    token,
    user,
    permissions,
    userProfile,
    userSettings,
    // 计算属性
    isLoggedIn,
    userRole,
    // 方法
    setToken,
    clearToken,
    setUser,
    setPermissions,
    loginAction,
    getUserInfoAction,
    logoutAction,
    getUserProfileAction,
    updateUserProfileAction,
    changePasswordAction,
    getUserSettingsAction,
    updateUserSettingsAction,
    applyTheme,
    hasPermission
  };
});
