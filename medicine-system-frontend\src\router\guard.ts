import type { Router } from 'vue-router';
import { useAuthStore } from '../store/auth';
import { ElMessage } from 'element-plus';

// 不需要登录的页面
const whiteList = ['/login'];

export function setupRouterGuard(router: Router) {
  // 全局前置守卫
  router.beforeEach(async (to, from, next) => {
    const authStore = useAuthStore();
    
    // 如果已登录
    if (authStore.isLoggedIn) {
      if (to.path === '/login') {
        // 已登录用户访问登录页，重定向到首页
        next('/dashboard');
      } else {
        // 检查是否有用户信息
        if (!authStore.user) {
          try {
            await authStore.getUserInfoAction();
            next();
          } catch (error) {
            // 获取用户信息失败，清除token并跳转到登录页
            authStore.clearToken();
            ElMessage.error('登录状态已过期，请重新登录');
            next('/login');
          }
        } else {
          next();
        }
      }
    } else {
      // 未登录
      if (whiteList.includes(to.path)) {
        // 在白名单中，直接放行
        next();
      } else {
        // 不在白名单中，跳转到登录页
        next('/login');
      }
    }
  });

  // 全局后置守卫
  router.afterEach((to) => {
    // 设置页面标题
    const title = getPageTitle(to.name as string);
    document.title = title;
  });
}

// 获取页面标题
function getPageTitle(routeName: string): string {
  const titleMap: Record<string, string> = {
    'Login': '登录 - 医药管理系统',
    'Dashboard': '仪表盘 - 医药管理系统',
    'Medicine': '药品管理 - 医药管理系统',
    'MedicineAdd': '添加药品 - 医药管理系统',
    'Inventory': '库存管理 - 医药管理系统',
    'Purchase': '采购管理 - 医药管理系统',
    'Sale': '销售管理 - 医药管理系统',
    'User': '用户管理 - 医药管理系统',
    'Role': '角色管理 - 医药管理系统',
    'Supplier': '供应商管理 - 医药管理系统',
    'Customer': '客户管理 - 医药管理系统',
    'Report': '报表统计 - 医药管理系统',
    'Setting': '系统设置 - 医药管理系统',
    'Log': '日志管理 - 医药管理系统'
  };
  return titleMap[routeName] || '医药管理系统';
}
