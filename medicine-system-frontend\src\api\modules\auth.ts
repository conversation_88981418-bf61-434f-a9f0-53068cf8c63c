import request from '../index';
import type { LoginForm, RegisterForm, LoginResult, User, UserProfile, ChangePasswordForm, UserSettings, AvatarUploadResult } from '@/types';

// 登录
export const login = (data: LoginForm) => {
  return request.post<LoginResult>('/auth/login', data);
};

// 注册
export const register = (data: RegisterForm) => {
  return request.post<User>('/auth/register', data);
};

// 获取用户信息
export const getUserInfo = () => {
  return request.get<User>('/auth/user-info');
};

// 登出
export const logout = () => {
  return request.post('/auth/logout');
};

// 刷新token
export const refreshToken = () => {
  return request.post<{ token: string }>('/auth/refresh-token');
};

// 获取当前用户详细信息
export const getCurrentUserProfile = () => {
  return request.get<UserProfile>('/auth/profile');
};

// 更新用户个人信息
export const updateUserProfile = (data: Partial<UserProfile>) => {
  return request.put<UserProfile>('/auth/profile', data);
};

// 修改密码
export const changePassword = (data: ChangePasswordForm) => {
  return request.post('/auth/change-password', data);
};

// 上传头像
export const uploadAvatar = (file: File) => {
  const formData = new FormData();
  formData.append('avatar', file);
  return request.post<AvatarUploadResult>('/auth/upload-avatar', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
};

// 获取用户偏好设置
export const getUserSettings = () => {
  return request.get<UserSettings>('/auth/settings');
};

// 更新用户偏好设置
export const updateUserSettings = (data: UserSettings) => {
  return request.put<UserSettings>('/auth/settings', data);
};
